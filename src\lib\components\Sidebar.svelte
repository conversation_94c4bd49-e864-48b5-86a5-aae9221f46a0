<script lang="ts">
  import {
    appSettings,
    updateAppSetting,
    availableModels,
    switchAIProvider,
    settingsLoaded
  } from '../stores/unifiedSettingsStore';
  import type { AIProviderType, TextAlignType, LineSpacingType } from '../types';
  import CustomModelSelect from './CustomModelSelect.svelte';
  import ModelSelect from './ModelSelect.svelte';
  import SystemPromptModal from './SystemPromptModal.svelte';

  // Modal state management
  let showSystemPromptModal = false;

  const openSystemPromptModal = () => showSystemPromptModal = true;
  const closeSystemPromptModal = () => showSystemPromptModal = false;

  // Available font options
  const fontFamilies = [
    { value: "Arial, sans-serif", name: "Arial" },
    { value: "'Courier New', Courier, monospace", name: "Courier New" },
    { value: "'Georgia', serif", name: "Georgia" },
    { value: "'Times New Roman', Times, serif", name: "Times New Roman" },
    { value: "'Verdana', Geneva, sans-serif", name: "<PERSON>erd<PERSON>" },
  ];

  // Text alignment options with SVG icons
  const textAlignOptions: { value: TextAlignType, name: string, svg: string }[] = [
    {
      value: 'left',
      name: 'Left',
      svg: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2 3h12v1H2V3zm0 3h8v1H2V6zm0 3h12v1H2V9zm0 3h8v1H2v-1z"/></svg>'
    },
    {
      value: 'center',
      name: 'Center',
      svg: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2 3h12v1H2V3zm2 3h8v1H4V6zm-2 3h12v1H2V9zm2 3h8v1H4v-1z"/></svg>'
    },
    {
      value: 'right',
      name: 'Right',
      svg: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2 3h12v1H2V3zm4 3h8v1H6V6zm-4 3h12v1H2V9zm4 3h8v1H6v-1z"/></svg>'
    },
    {
      value: 'justify',
      name: 'Justify',
      svg: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2 3h12v1H2V3zm0 3h12v1H2V6zm0 3h12v1H2V9zm0 3h12v1H2v-1z"/></svg>'
    }
  ];

  // Line spacing options
  const lineSpacingOptions: { value: LineSpacingType, name: string }[] = [
    { value: '1.0', name: 'Single' },
    { value: '1.15', name: '1.15' },
    { value: '1.5', name: '1.5' },
    { value: '2.0', name: 'Double' },
    { value: '2.5', name: '2.5' },
    { value: '3.0', name: 'Triple' }
  ];

  // Available AI providers
  const aiProviders: { value: AIProviderType, name: string }[] = [
    { value: "gemini", name: "Gemini" },
    { value: "openai", name: "OpenAI" },
    { value: "custom", name: "Custom (OpenAI-like)" },
  ];

  // Handle AI provider change
  const handleProviderChange = (event: Event) => {
    const newProvider = (event.target as HTMLSelectElement).value as AIProviderType;
    switchAIProvider(newProvider);
  };

  // Handle numeric input with validation
  const handleNumberInput = (key: string, min: number, max: number, defaultValue: number) =>
    (event: Event) => {
      const value = parseInt((event.target as HTMLInputElement).value) || defaultValue;
      updateAppSetting(key as any, Math.max(min, Math.min(max, value)));
    };
</script>

{#if $settingsLoaded}
<!-- Settings sidebar with font and AI configuration -->
<aside class="sidebar">
  <h3>Settings</h3>

  <!-- Font settings -->
  <div class="settings-group">
    <label for="fontFamily">Font Family:</label>
    <select
      id="fontFamily"
      bind:value={$appSettings.fontFamily}
      on:change={(e) => updateAppSetting('fontFamily', (e.target as HTMLSelectElement).value)}
    >
      {#each fontFamilies as font}
        <option value={font.value}>{font.name}</option>
      {/each}
    </select>
  </div>

  <div class="settings-group">
    <label for="fontSize">Font Size (px):</label>
    <input
      type="number"
      id="fontSize"
      min="8"
      max="72"
      bind:value={$appSettings.fontSize}
      on:change={handleNumberInput('fontSize', 8, 72, 16)}
    />
  </div>

  <!-- Text formatting settings -->
  <div class="settings-group">
    <label for="text-formatting">Text Formatting:</label>

    <!-- Row 1: Text alignment buttons -->
    <div class="formatting-row" id="text-formatting">
      <span class="formatting-label">Alignment:</span>
      <div class="button-group">
        {#each textAlignOptions as option}
          <button
            class="format-button"
            class:active={$appSettings.textAlign === option.value}
            on:click={() => updateAppSetting('textAlign', option.value)}
            title={option.name}
          >
            {@html option.svg}
          </button>
        {/each}
      </div>
    </div>

    <!-- Row 2: Line spacing -->
    <div class="formatting-row">
      <span class="formatting-label">Line Spacing:</span>
      <select
        class="line-spacing-select"
        bind:value={$appSettings.lineSpacing}
        on:change={(e) => updateAppSetting('lineSpacing', (e.target as HTMLSelectElement).value as LineSpacingType)}
      >
        {#each lineSpacingOptions as option}
          <option value={option.value}>{option.name}</option>
        {/each}
      </select>
    </div>
  </div>

  <!-- Thinking panel toggle -->
  <div class="settings-group">
    <label class="checkbox-label">
      <input
        type="checkbox"
        id="showThinkingPanel"
        bind:checked={$appSettings.showThinkingPanel}
        on:change={(e) => updateAppSetting('showThinkingPanel', (e.target as HTMLInputElement).checked)}
      />
      Show AI Thinking Process
    </label>
    <p class="setting-description">
      Display the AI's thinking process when available
    </p>
  </div>

  <!-- System Prompt button -->
  <div class="settings-group">
    <button class="system-prompt-button" on:click={openSystemPromptModal}>
      📝 System Prompts
    </button>
    <p class="setting-description">
      Customize AI behavior for different features
    </p>
  </div>

  <h3>AI Configuration</h3>

  <!-- AI Provider selection -->
  <div class="settings-group">
    <label for="aiProvider">AI Provider:</label>
    <select id="aiProvider" on:change={handleProviderChange}>
      {#each aiProviders as provider}
        <option value={provider.value}>{provider.name}</option>
      {/each}
    </select>
  </div>

  <!-- AI Model selection -->
  <div class="settings-group">
    <label for="aiModel">AI Model:</label>
    {#if $appSettings.aiProvider === 'custom'}
      <!-- Custom model input with history -->
      <CustomModelSelect
        bind:value={$appSettings.aiModel}
        placeholder="Enter custom model name"
        on:input={(e) => updateAppSetting('aiModel', e.detail)}
        on:change={(e) => updateAppSetting('aiModel', e.detail)}
      />
    {:else}
      <!-- Predefined model selection -->
      <ModelSelect
        models={$availableModels}
        value={$appSettings.aiModel}
        onchange={(modelId) => updateAppSetting('aiModel', modelId)}
      />
    {/if}
  </div>

  <!-- Autocomplete context length setting -->
  <div class="settings-group">
    <label for="autocompleteContextLength">Autocomplete Context Length:</label>
    <input
      type="number"
      id="autocompleteContextLength"
      min="100"
      max="10000"
      step="100"
      bind:value={$appSettings.autocompleteContextLength}
      on:change={handleNumberInput('autocompleteContextLength', 100, 10000, 1000)}
    />
    <p class="setting-description">
      Number of characters to use as context for autocomplete (100-10000)
    </p>
  </div>
</aside>
{:else}
<!-- Loading state -->
<aside class="sidebar">
  <h3>Settings</h3>
  <div class="loading-placeholder">Loading settings...</div>
</aside>
{/if}

<!-- System Prompt configuration modal -->
<SystemPromptModal bind:isOpen={showSystemPromptModal} close={closeSystemPromptModal} />

<style>
  .sidebar {
    width: 280px;
    background-color: var(--color-sidebar-bg);
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
    flex-shrink: 0;
    border-left: 1px solid var(--color-border);
    transition: all 0.2s ease;
  }
  .sidebar h3 {
    margin-top: 0;
    color: var(--color-text-primary);
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 5px;
    transition: all 0.2s ease;
  }
  .settings-group {
    margin-bottom: 20px;
  }
  .settings-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 0.9em;
    color: var(--color-text-primary);
    transition: color 0.2s ease;
  }
  .settings-group .checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }
  .settings-group .checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
  }
  .settings-group select,
  .settings-group input[type="number"] {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    border: 1px solid var(--color-border);
    border-radius: 3px;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    transition: all 0.2s ease;
  }

  .settings-group select:hover,
  .settings-group input[type="number"]:hover {
    border-color: var(--color-accent);
  }

  .settings-group select:focus,
  .settings-group input[type="number"]:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.25);
  }
  .setting-description {
    font-size: 0.85em;
    margin-top: 5px;
    color: var(--color-text-primary);
    opacity: 0.7;
    min-height: 1.2em;
    line-height: 1.3;
    transition: color 0.2s ease;
  }
   /* Button styles removed - no longer needed since settings auto-save */

  .loading-placeholder {
    padding: 20px;
    text-align: center;
    color: var(--color-text-primary);
    opacity: 0.7;
    font-style: italic;
    transition: color 0.2s ease;
  }

  /* Text formatting controls */
  .formatting-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
  }

  .formatting-label {
    font-size: 0.85em;
    color: var(--color-text-primary);
    opacity: 0.8;
    min-width: 70px;
    flex-shrink: 0;
  }

  .button-group {
    display: flex;
    gap: 2px;
    border: 1px solid var(--color-border);
    border-radius: 3px;
    overflow: hidden;
  }

  .format-button {
    padding: 6px 10px;
    border: none;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    border-right: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
  }

  .format-button:last-child {
    border-right: none;
  }

  .format-button:hover {
    background-color: var(--color-toggle-hover);
  }

  .format-button.active {
    background-color: var(--color-accent);
    color: white;
  }

  .format-button :global(svg) {
    width: 16px;
    height: 16px;
    fill: currentColor;
  }

  .line-spacing-select {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid var(--color-border);
    border-radius: 3px;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    font-size: 0.9em;
    transition: all 0.2s ease;
  }

  .line-spacing-select:hover {
    border-color: var(--color-accent);
  }

  .line-spacing-select:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.25);
  }

  .system-prompt-button {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .system-prompt-button:hover {
    background-color: var(--color-toggle-hover);
    border-color: var(--color-accent);
  }

  .system-prompt-button:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.25);
  }
</style>
